#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日报生成器 - 集成图表版本
将综合图表集成到日报HTML中，生成完整的分析报告
"""

import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.comprehensive_chart_generator import ComprehensiveChartGenerator
from utils.directory_manager import get_output_path


class DailyReportWithCharts:
    """集成图表的日报生成器"""
    
    def __init__(self):
        """初始化日报生成器"""
        self.chart_generator = ComprehensiveChartGenerator()
        self.report_date = datetime.now()
    
    def generate_market_summary(self):
        """生成市场概况文字分析"""
        
        # 获取基础数据用于分析
        ih_data = self.chart_generator.get_one_year_data('IH')
        if_data = self.chart_generator.get_one_year_data('IF')
        im_data = self.chart_generator.get_one_year_data('IM')
        
        # 计算关键指标
        ih_change = (ih_data['close'].iloc[-1] / ih_data['close'].iloc[-2] - 1) * 100
        if_change = (if_data['close'].iloc[-1] / if_data['close'].iloc[-2] - 1) * 100
        im_change = (im_data['close'].iloc[-1] / im_data['close'].iloc[-2] - 1) * 100
        
        # 计算波动率
        ih_vol = ih_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100
        if_vol = if_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100
        im_vol = im_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100
        
        summary = f"""
## 📊 市场概况

### 🎯 主要指数表现
- **IH (上证50)**: {ih_data['close'].iloc[-1]:.2f} ({ih_change:+.2f}%)
- **IF (沪深300)**: {if_data['close'].iloc[-1]:.2f} ({if_change:+.2f}%)
- **IM (中证1000)**: {im_data['close'].iloc[-1]:.2f} ({im_change:+.2f}%)

### 📈 波动率水平
- **IH 20日历史波动率**: {ih_vol:.2f}%
- **IF 20日历史波动率**: {if_vol:.2f}%
- **IM 20日历史波动率**: {im_vol:.2f}%

### 🔍 市场特征
- 从一年期数据来看，三大股指期货呈现不同的波动特征
- IH作为大盘蓝筹代表，波动相对较小
- IM作为小盘成长代表，波动幅度较大
- IF介于两者之间，反映市场整体情况

### 📊 技术分析要点
1. **趋势分析**: 基于一年期价格走势，识别主要趋势方向
2. **波动率分析**: IV-HV价差反映市场情绪和期权定价效率
3. **期权流向**: PCR指标显示投资者看涨看跌倾向
4. **期限结构**: 不同到期日期权的隐含波动率分布
"""
        return summary
    
    def generate_volatility_analysis(self):
        """生成波动率分析"""
        
        analysis = """
## 🌊 波动率深度分析

### 📈 隐含波动率 vs 历史波动率
通过IV-HV对比分析，我们可以观察到：
- **期权定价效率**: IV高于HV时，期权可能被高估
- **市场情绪指标**: IV-HV价差反映投资者对未来波动的预期
- **交易机会识别**: 价差异常时可能存在套利机会

### 😊 波动率微笑现象
波动率微笑曲线揭示了期权市场的重要特征：
- **深度虚值期权**: 通常具有较高的隐含波动率
- **平值期权**: 隐含波动率相对较低
- **微笑形状**: 反映市场对极端事件的担忧

### 🕐 期限结构分析
不同到期日的隐含波动率分布：
- **短期期权**: 受即期事件影响较大
- **长期期权**: 反映长期市场预期
- **期限升水**: 通常长期波动率高于短期

### 🌊 3D波动率曲面
三维波动率曲面综合展示：
- **执行价格维度**: 不同行权价的波动率差异
- **到期时间维度**: 期限结构的完整展现
- **波动率水平**: 整体市场波动率环境
"""
        return analysis
    
    def generate_option_analysis(self):
        """生成期权分析"""
        
        analysis = """
## 🎯 期权市场分析

### 📊 PCR (Put/Call Ratio) 解读
PCR指标是重要的市场情绪指标：
- **PCR > 1.3**: 市场偏悲观，看跌期权交易活跃
- **PCR < 0.7**: 市场偏乐观，看涨期权交易活跃
- **PCR ≈ 1.0**: 市场情绪相对平衡

### 🥧 持仓结构分析
期权持仓分布反映投资者策略偏好：
- **看涨期权**: 直接做多策略
- **看跌期权**: 对冲或做空策略
- **跨式组合**: 波动率交易策略
- **宽跨式组合**: 大幅波动预期策略

### 💡 交易策略建议
基于当前市场状况：
1. **波动率交易**: 关注IV-HV价差机会
2. **方向性交易**: 结合技术分析确定趋势
3. **套利交易**: 利用期限结构和微笑曲线异常
4. **风险管理**: 动态调整持仓和对冲比例
"""
        return analysis
    
    def generate_comprehensive_daily_report(self):
        """生成包含图表的综合日报"""
        logger.info("开始生成包含图表的综合日报...")
        
        # 生成所有图表
        charts = {
            'volatility_table': self.chart_generator.generate_volatility_core_table(),
            'price_trends': self.chart_generator.generate_futures_price_trends(),
            'iv_hv_trends': self.chart_generator.generate_iv_hv_trends(),
            'pcr_trends': self.chart_generator.generate_pcr_trends(),
            'volatility_smile': self.chart_generator.generate_volatility_smile(),
            'term_structure': self.chart_generator.generate_volatility_term_structure(),
            'volatility_surface': self.chart_generator.generate_volatility_surface(),
            'iv_hv_spread': self.chart_generator.generate_iv_hv_spread(),
            'position_distribution': self.chart_generator.generate_option_position_distribution()
        }
        
        # 生成文字分析
        market_summary = self.generate_market_summary()
        volatility_analysis = self.generate_volatility_analysis()
        option_analysis = self.generate_option_analysis()
        
        # 创建完整的HTML日报
        html_content = self.create_daily_report_html(charts, market_summary, volatility_analysis, option_analysis)
        
        # 保存文件
        filename = f"daily_report_with_charts_{self.report_date.strftime('%Y%m%d_%H%M%S')}.html"
        output_path = get_output_path('report', filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✓ 综合日报已生成: {output_path}")
        return output_path
    
    def create_daily_report_html(self, charts, market_summary, volatility_analysis, option_analysis):
        """创建日报HTML模板"""
        
        # 导入plotly并生成图表HTML
        import plotly.offline as pyo
        
        chart_htmls = {}
        for name, fig in charts.items():
            chart_htmls[name] = pyo.plot(fig, output_type='div', include_plotlyjs=False)
        
        # 将Markdown转换为HTML (简单版本)
        def markdown_to_html(text):
            # 简单的Markdown转HTML
            text = text.replace('### ', '<h3>').replace('\n### ', '</h3>\n<h3>')
            text = text.replace('## ', '<h2>').replace('\n## ', '</h2>\n<h2>')
            text = text.replace('# ', '<h1>').replace('\n# ', '</h1>\n<h1>')
            text = text.replace('- **', '<li><strong>').replace('**:', '</strong>:')
            text = text.replace('- ', '<li>').replace('\n<li>', '</li>\n<li>')
            text = text.replace('\n\n', '</p>\n<p>')
            text = f'<p>{text}</p>'
            text = text.replace('<p><h', '<h').replace('</h3></p>', '</h3>')
            text = text.replace('<p><h', '<h').replace('</h2></p>', '</h2>')
            text = text.replace('<p><h', '<h').replace('</h1></p>', '</h1>')
            text = text.replace('<p><li>', '<ul><li>').replace('</li></p>', '</li></ul>')
            return text
        
        # 创建完整HTML
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国股指期货日报 - {self.report_date.strftime('%Y年%m月%d日')}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            line-height: 1.6;
        }}
        .header {{
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
        }}
        .header h1 {{
            margin: 0;
            font-size: 3em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }}
        .header p {{
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.95;
        }}
        .content-section {{
            background: white;
            margin: 30px 0;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .chart-container {{
            background: white;
            margin: 25px 0;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 3px 6px rgba(0,0,0,0.08);
            border-left: 5px solid #667eea;
        }}
        .chart-title {{
            font-size: 1.6em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }}
        .grid-container {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin: 25px 0;
        }}
        .full-width {{
            grid-column: 1 / -1;
        }}
        .text-content {{
            font-size: 1.1em;
            color: #444;
        }}
        .text-content h2 {{
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-top: 30px;
        }}
        .text-content h3 {{
            color: #555;
            margin-top: 25px;
        }}
        .text-content ul {{
            padding-left: 20px;
        }}
        .text-content li {{
            margin: 8px 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding: 30px;
            background: linear-gradient(135deg, #333 0%, #555 100%);
            color: white;
            border-radius: 15px;
        }}
        @media (max-width: 768px) {{
            .grid-container {{
                grid-template-columns: 1fr;
            }}
            .header h1 {{
                font-size: 2em;
            }}
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 中国股指期货日报</h1>
        <p>{self.report_date.strftime('%Y年%m月%d日')} | 数据期间: 一年期 | 合约: IH、IF、IM</p>
    </div>
    
    <!-- 市场概况 -->
    <div class="content-section">
        <div class="text-content">
            {markdown_to_html(market_summary)}
        </div>
    </div>
    
    <!-- 核心指标表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 波动率核心指标表</div>
        {chart_htmls['volatility_table']}
    </div>
    
    <!-- 价格走势和PCR -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">📊 股指期货价格走势图</div>
            {chart_htmls['price_trends']}
        </div>
        <div class="chart-container">
            <div class="chart-title">📊 PCR走势图</div>
            {chart_htmls['pcr_trends']}
        </div>
    </div>
    
    <!-- 波动率分析文字 -->
    <div class="content-section">
        <div class="text-content">
            {markdown_to_html(volatility_analysis)}
        </div>
    </div>
    
    <!-- IV-HV分析图表 -->
    <div class="chart-container full-width">
        <div class="chart-title">📈 IV-HV走势对比图</div>
        {chart_htmls['iv_hv_trends']}
    </div>
    
    <div class="chart-container full-width">
        <div class="chart-title">📊 IV-HV价差图</div>
        {chart_htmls['iv_hv_spread']}
    </div>
    
    <!-- 波动率深度分析 -->
    <div class="grid-container">
        <div class="chart-container">
            <div class="chart-title">😊 波动率微笑曲线</div>
            {chart_htmls['volatility_smile']}
        </div>
        <div class="chart-container">
            <div class="chart-title">📈 波动率期限结构</div>
            {chart_htmls['term_structure']}
        </div>
    </div>
    
    <!-- 3D波动率曲面 -->
    <div class="chart-container full-width">
        <div class="chart-title">🌊 波动率曲面 (3D)</div>
        {chart_htmls['volatility_surface']}
    </div>
    
    <!-- 期权分析文字 -->
    <div class="content-section">
        <div class="text-content">
            {markdown_to_html(option_analysis)}
        </div>
    </div>
    
    <!-- 期权持仓分布 -->
    <div class="chart-container full-width">
        <div class="chart-title">🥧 期权持仓分布图</div>
        {chart_htmls['position_distribution']}
    </div>
    
    <div class="footer">
        <p>📊 中国股指期货报告自动生成系统 | 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>💡 本报告基于一年期历史数据生成，包含IH、IF、IM三大股指期货合约的全面分析</p>
        <p>⚠️ 本报告仅供参考，投资有风险，入市需谨慎</p>
    </div>
</body>
</html>
"""
        
        return html_template

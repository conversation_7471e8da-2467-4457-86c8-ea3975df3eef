#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告编排生成模块
负责整合量化洞察、舆情摘要，使用AI生成最终报告
"""

import os
import json
from datetime import datetime
from pathlib import Path
import openai
from loguru import logger

from utils.directory_manager import get_output_path


class ReportComposer:
    """报告编排器"""
    
    def __init__(self):
        """初始化报告编排器"""
        self.openai_client = None
        self.setup_openai()
    
    def setup_openai(self):
        """设置OpenAI客户端"""
        try:
            # 从配置中获取API密钥
            from config.config import OPENAI_CONFIG
            
            if OPENAI_CONFIG["api_key"] == "your_openai_api_key_here":
                logger.warning("OpenAI API密钥未设置，将使用模拟报告生成")
                return False
            
            # 设置OpenAI客户端
            openai.api_key = OPENAI_CONFIG["api_key"]
            self.openai_client = openai
            
            logger.info("✓ OpenAI客户端设置完成")
            return True
            
        except Exception as e:
            logger.error(f"OpenAI设置失败: {e}")
            return False
    
    def summarize_articles(self, article_data):
        """使用AI总结文章内容"""
        logger.info("正在总结舆情文章...")
        
        try:
            # 整理文章内容
            articles_text = ""
            
            if 'wechat' in article_data:
                for article in article_data['wechat']:
                    articles_text += f"标题: {article['title']}\n"
                    articles_text += f"来源: {article['source']}\n"
                    articles_text += f"内容: {article['content']}\n"
                    articles_text += "-" * 30 + "\n"
            
            if 'douyin' in article_data and article_data['douyin']:
                # 读取抖音内容文件
                douyin_file = article_data['douyin']['file_path']
                if Path(douyin_file).exists():
                    with open(douyin_file, 'r', encoding='utf-8') as f:
                        articles_text += f.read()
            
            if not articles_text.strip():
                logger.warning("没有文章内容可供总结")
                return self.generate_mock_summary()
            
            # 使用AI总结
            if self.openai_client:
                summary = self.ai_summarize_text(articles_text)
            else:
                summary = self.generate_mock_summary()
            
            logger.info("✓ 文章总结完成")
            return summary
            
        except Exception as e:
            logger.error(f"文章总结失败: {e}")
            return self.generate_mock_summary()
    
    def ai_summarize_text(self, text):
        """使用AI总结文本"""
        try:
            prompt = f"""
            请对以下金融舆情文章进行专业总结，提取关键信息：

            {text}

            请按以下格式输出：
            1. 核心事件摘要（3-5个要点）
            2. 市场情绪分析
            3. 关键数据提取
            4. 对股指期货市场的影响分析

            要求：
            - 语言专业、简洁
            - 突出对期货市场的影响
            - 提取具体的数据和时间点
            """
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "你是一位专业的金融分析师，擅长分析市场舆情和数据。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"AI总结失败: {e}")
            return self.generate_mock_summary()
    
    def generate_mock_summary(self):
        """生成模拟总结"""
        return """
        **核心事件摘要：**
        1. A股三大指数全线收跌，上证指数跌0.94%，市场情绪转向谨慎
        2. 美国7月非农数据爆冷，新增就业仅7.3万人，远低于预期
        3. 全球降息预期升温，9月降息概率飙升至75.5%
        4. 期权隐含波动率处于高位，IV-HV分位值均在70%以上
        5. 北向资金净流出45.67亿元，外资情绪偏谨慎

        **市场情绪分析：**
        市场整体情绪从乐观转向谨慎，投资者对经济复苏预期有所降温。
        机构普遍认为短期将进入震荡调整，但中长期仍看好政策底支撑。

        **关键数据提取：**
        - PMI数据不及预期，经济基本面承压
        - 两市成交额萎缩至1.95万亿元
        - 期权PCR指标显示情绪偏向乐观
        - 美股暴跌，道指跌超600点

        **对股指期货市场影响：**
        短期下行压力增大，但全球流动性宽松预期提供支撑。
        期权高波动率为卖方策略提供机会，建议关注熊市价差和卖出宽跨式策略。
        """
    
    def integrate_chart_insights(self, chart_data):
        """整合图表洞察"""
        logger.info("正在整合图表分析洞察...")
        
        insights = []
        
        for chart_name, chart_info in chart_data.items():
            if 'insights' in chart_info:
                insights.append(f"**{chart_name}**: {chart_info['insights']}")
        
        if not insights:
            insights = [
                "**市场情绪面板**: 市场整体呈现下跌格局，资金流出明显",
                "**期货价格走势**: 三大股指期货呈现分化走势，IM波动最大"
            ]
        
        return "\n".join(insights)
    
    def generate_final_report(self, chart_insights, article_summary):
        """生成最终报告"""
        logger.info("正在生成最终报告...")
        
        try:
            # 读取报告模板
            template_path = Path("项目参考文档/最终生成报告模版.md")
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template = f.read()
            else:
                template = self.get_default_template()
            
            # 准备报告内容
            current_date = datetime.now().strftime('%Y年%m月%d日')
            
            if self.openai_client:
                report = self.ai_generate_report(template, chart_insights, article_summary, current_date)
            else:
                report = self.generate_mock_report(current_date, chart_insights, article_summary)
            
            logger.info("✓ 最终报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"生成最终报告失败: {e}")
            return self.generate_mock_report(
                datetime.now().strftime('%Y年%m月%d日'),
                chart_insights,
                article_summary
            )
    
    def ai_generate_report(self, template, chart_insights, article_summary, current_date):
        """使用AI生成报告"""
        try:
            prompt = f"""
            基于以下信息，生成一份专业的股指期货分析报告：

            **日期**: {current_date}
            
            **图表分析洞察**:
            {chart_insights}
            
            **舆情文章总结**:
            {article_summary}
            
            **报告模板参考**:
            {template}
            
            请生成一份完整的报告，包括：
            1. 晨会口语分享稿（5分钟）
            2. 详细策略报告
            
            要求：
            - 语言专业、逻辑清晰
            - 结合实际数据和市场情况
            - 提供具体的策略建议
            - 包含风险提示
            """
            
            response = self.openai_client.ChatCompletion.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "你是一位资深的期货分析师，擅长撰写专业的市场分析报告。"},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=4000,
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error(f"AI报告生成失败: {e}")
            return self.generate_mock_report(current_date, chart_insights, article_summary)
    
    def generate_mock_report(self, current_date, chart_insights, article_summary):
        """生成模拟报告"""
        return f"""
# 股指期货与期权晨会日报 ({current_date})

## 【晨会口语分享稿】

**(一、开场与市场概览，约1分钟)**
"行情方面，A股指数昨日全线收跌，上证指数下跌0.94%，深成指与创业板指跌幅均超过0.7%，市场呈现普跌格局。股指期货市场，三大主力合约同步下挫，且持仓量均出现下降。技术形态上，主要指数均已跌破关键短期均线，形成了破位下行的技术形态。"

**(二、关键驱动因素分析，约1.5分钟)**
"宏观因素方面，按重要性排序：
第一，国内强政策预期与弱现实的矛盾激化。7月PMI数据低于荣枯线，确认了经济基本面的收缩压力。
第二，美国非农数据爆冷，新增就业仅7.3万人，远低于预期，全球降息预期升温。
第三，期权市场波动率定价异常昂贵，为策略构建提供机会。"

**(三、衍生品市场深度论证，约1.5分钟)**
"期权市场显示，三大股指期权的IV-HV分位值依然处在70%以上的极端高位。这表明尽管指数已经下跌，但期权市场对未来更大波动的预期依然强烈，期权价格异常昂贵。这种定价结构为卖出波动率策略提供了极高的安全垫。"

**(四、策略建议，约1分钟)**
"建议构建熊市看跌价差策略，即买入平值看跌期权，同时卖出更虚值的看跌期权以降低成本。这样既能顺应下跌趋势，又能从高波动率中获利。"

**(五、风险提示，约30秒)**
"主要风险在于市场出现超预期反弹，特别是政策面的积极信号可能引发快速反弹。需密切关注指数在关键支撑位的表现。"

---

## 【详细策略报告】

### 🧭 一、核心叙事与逻辑链

**今日核心叙事**: 强预期退潮，市场进入风险释放与价值重估阶段。

**图表分析洞察**:
{chart_insights}

**舆情分析摘要**:
{article_summary}

### 🔍 二、行情概览与技术分析

市场整体呈现下跌格局，技术形态转弱，短期进入调整阶段。

### 🌐 三、宏观事件与资金面追踪

关键宏观事件包括PMI数据不及预期、美国非农数据爆冷等，资金面呈现净流出状态。

### 🧩 四、衍生品市场深度剖析

期权隐含波动率处于高位，PCR指标显示市场情绪偏向谨慎，为卖方策略提供机会。

### 🎯 五、今日交易策略与风控

**核心策略**: 熊市看跌价差策略
**风控要求**: 严格控制仓位，设置止损位

### ⚠️ 六、核心风险提示

1. 政策面超预期利好风险
2. 外围市场大幅波动风险  
3. 流动性突然收紧风险

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*免责声明: 本报告仅供参考，不构成投资建议*
        """
    
    def get_default_template(self):
        """获取默认模板"""
        return """
        # 股指期货与期权晨会日报模板
        
        ## 【晨会口语分享稿】
        ## 【详细策略报告】
        """
    
    def compose_report(self, chart_data, article_data):
        """编排生成完整报告"""
        logger.info("开始编排生成完整报告...")
        
        try:
            # 1. 总结舆情文章
            article_summary = self.summarize_articles(article_data)
            
            # 2. 整合图表洞察
            chart_insights = self.integrate_chart_insights(chart_data)
            
            # 3. 生成最终报告
            final_report = self.generate_final_report(chart_insights, article_summary)
            
            # 4. 保存报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"daily_report_{timestamp}.md"
            output_path = get_output_path("report", filename)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_report)
            
            logger.info(f"✓ 完整报告已保存: {output_path}")
            
            return {
                "file_path": str(output_path),
                "filename": filename,
                "content_length": len(final_report),
                "generation_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"报告编排失败: {e}")
            return None

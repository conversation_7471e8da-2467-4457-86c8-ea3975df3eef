#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志设置工具
"""

import os
import sys
from pathlib import Path
from loguru import logger


def setup_logger():
    """设置系统日志"""
    
    # 移除默认的控制台处理器
    logger.remove()
    
    # 确保日志目录存在
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 控制台输出格式
    console_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 文件输出格式
    file_format = (
        "{time:YYYY-MM-DD HH:mm:ss} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{message}"
    )
    
    # 添加控制台处理器
    logger.add(
        sys.stdout,
        format=console_format,
        level="INFO",
        colorize=True
    )
    
    # 添加文件处理器 - 所有日志
    logger.add(
        "logs/system.log",
        format=file_format,
        level="DEBUG",
        rotation="1 day",
        retention="30 days",
        compression="zip",
        encoding="utf-8"
    )
    
    # 添加错误日志文件
    logger.add(
        "logs/error.log",
        format=file_format,
        level="ERROR",
        rotation="1 week",
        retention="12 weeks",
        compression="zip",
        encoding="utf-8"
    )
    
    return logger

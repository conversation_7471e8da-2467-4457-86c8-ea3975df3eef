# 中国股指期货报告自动生成系统 - 核心规则配置
# Version: 1.0
# Last Updated: 2025-08-04

# ==================== 系统全局配置 ====================
system:
  version: "1.0"
  environment: "production"
  timezone: "Asia/Shanghai"
  
# ==================== 数据源配置 ====================
data_sources:
  # 舆情监控源
  sentiment_sources:
    wechat_accounts:
      - "财经早餐"
      - "陆家嘴财经早餐"
      - "华泰期货研究"
    douyin_accounts:
      - "小张小张吃饭用缸"
      - "清醒高师傅"
      - "暖叔"
    keywords:
      - "IF主力"
      - "降息"
      - "英伟达"
      - "PMI"
      - "期权"
      - "波动率"
  
  # 金融数据源
  financial_apis:
    wind:
      enabled: true
      timeout: 30
    ifind:
      enabled: true
      timeout: 30

# ==================== 图表分析规则库 ====================
chart_analysis_rules:
  # PCR指标分析规则
  pcr_analysis:
    - condition: "pcr_value > 1.2"
      signal: "bearish"
      weight: 4
      description: "PCR值过高，市场恐慌情绪浓厚"
    - condition: "pcr_value < 0.6"
      signal: "bullish"
      weight: 3
      description: "PCR值过低，市场过度乐观"
    - condition: "0.6 <= pcr_value <= 1.2"
      signal: "neutral"
      weight: 2
      description: "PCR值正常，市场情绪平衡"

  # IV-HV价差分析规则
  iv_hv_spread:
    - condition: "iv_hv_percentile > 85"
      signal: "sell_volatility"
      weight: 5
      description: "IV-HV分位值极高，期权定价过贵，适合卖出波动率"
    - condition: "iv_hv_percentile < 15"
      signal: "buy_volatility"
      weight: 4
      description: "IV-HV分位值极低，期权定价便宜，适合买入波动率"
    - condition: "70 <= iv_hv_percentile <= 85"
      signal: "cautious_sell"
      weight: 3
      description: "IV-HV分位值较高，谨慎卖出波动率"

  # 技术形态分析规则
  technical_patterns:
    - condition: "price_below_ma5 and price_below_ma10"
      signal: "bearish_trend"
      weight: 3
      description: "价格跌破短期均线，趋势转空"
    - condition: "price_above_ma5 and price_above_ma10"
      signal: "bullish_trend"
      weight: 3
      description: "价格站上短期均线，趋势向好"

# ==================== 事件权重配置 ====================
event_weights:
  macro_events:
    pmi_data: 5
    interest_rate_decision: 5
    policy_announcement: 4
    trade_war: 4
  
  market_events:
    volume_surge: 3
    margin_trading_change: 3
    foreign_capital_flow: 3
    volatility_spike: 4

# ==================== 期权策略剧本库 ====================
option_strategy_playbook:
  # 熊市看跌价差策略
  bear_put_spread:
    trigger_conditions:
      - market_sentiment: "bearish"
      - iv_hv_percentile: "> 70"
      - trend_signal: "bearish_trend"
    construction:
      - action: "buy"
        option_type: "put"
        moneyness: "atm_or_slight_otm"
      - action: "sell"
        option_type: "put"
        moneyness: "deeper_otm"
    risk_management:
      max_loss: "net_premium_paid"
      profit_target: "50% of max profit"
      stop_loss: "75% of max loss"

  # 卖出宽跨式策略
  short_strangle:
    trigger_conditions:
      - market_sentiment: "neutral"
      - iv_hv_percentile: "> 80"
      - expected_movement: "range_bound"
    construction:
      - action: "sell"
        option_type: "call"
        moneyness: "otm"
      - action: "sell"
        option_type: "put"
        moneyness: "otm"
    risk_management:
      margin_requirement: "monitor_closely"
      adjustment_trigger: "underlying_near_strike"

  # 牛市价差策略
  bull_call_spread:
    trigger_conditions:
      - market_sentiment: "bullish"
      - liquidity_expectation: "improving"
      - index_type: "small_cap_preferred"
    construction:
      - action: "buy"
        option_type: "call"
        moneyness: "atm"
      - action: "sell"
        option_type: "call"
        moneyness: "otm"

# ==================== 情绪分析规则 ====================
sentiment_analysis:
  scoring_rules:
    positive_keywords:
      - "利好": 2
      - "上涨": 1
      - "看多": 2
      - "机会": 1
    negative_keywords:
      - "利空": -2
      - "下跌": -1
      - "看空": -2
      - "风险": -1
    
  aggregation_weights:
    institutional_sources: 0.6
    retail_sources: 0.3
    social_media: 0.1

# ==================== 报告生成规则 ====================
report_generation:
  # 晨会分享稿规则
  morning_briefing:
    max_duration_minutes: 5
    sections:
      market_overview: 1.0  # 分钟
      key_drivers: 1.5
      derivatives_analysis: 1.5
      strategy_recommendation: 1.0
      risk_warning: 0.5
  
  # 详细报告规则
  detailed_report:
    required_sections:
      - "核心叙事与逻辑链"
      - "行情概览与技术分析"
      - "宏观事件与资金面追踪"
      - "衍生品市场深度剖析"
      - "今日交易策略与风控"
      - "核心风险提示"
    
    chart_requirements:
      minimum_charts: 8
      required_chart_types:
        - "pcr_trend"
        - "iv_hv_spread"
        - "volatility_surface"
        - "option_oi_distribution"

# ==================== 风控规则 ====================
risk_management:
  position_limits:
    directional_strategies: 0.5  # 最大50%仓位
    volatility_strategies: 0.7   # 最大70%仓位
  
  stop_loss_rules:
    trend_strategies: 0.02  # 2%止损
    volatility_strategies: 0.05  # 5%止损
  
  market_conditions:
    high_volatility_threshold: 20  # IV > 20%时降低仓位
    low_liquidity_threshold: 1000  # 成交量 < 1000手时谨慎操作

# ==================== 系统学习与优化 ====================
learning_rules:
  feedback_collection:
    analyst_corrections: true
    performance_tracking: true
    strategy_effectiveness: true
  
  model_update_triggers:
    accuracy_threshold: 0.85
    feedback_count_threshold: 100
    time_based_update: "monthly"

# ==================== 输出格式配置 ====================
output_formats:
  supported_formats:
    - "markdown"
    - "pdf"
    - "html"
  
  distribution:
    email_recipients:
      - "<EMAIL>"
      - "<EMAIL>"
    storage_path: "/reports/daily/"
    backup_retention_days: 90
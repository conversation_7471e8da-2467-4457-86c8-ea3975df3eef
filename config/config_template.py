# 中国股指期货报告自动生成系统 - 配置模板
# 请复制此文件为 config.py 并填入您的实际配置信息

import os
from datetime import datetime

# ==================== API配置 ====================

# OpenAI API配置
OPENAI_CONFIG = {
    "api_key": "your_openai_api_key_here",  # 请填入您的OpenAI API密钥
    "model": "gpt-4",
    "max_tokens": 4000,
    "temperature": 0.3,
    "timeout": 60
}

# Wind API配置
WIND_CONFIG = {
    "timeout": 30,
    "retry_times": 3,
    "retry_delay": 5
}

# ==================== 数据源配置 ====================

# 股指期货合约配置
FUTURES_CONTRACTS = {
    "IF": "IF.CFE",  # 沪深300股指期货
    "IH": "IH.CFE",  # 上证50股指期货
    "IM": "IM.CFE"   # 中证1000股指期货
}

# 期权合约配置
OPTIONS_CONTRACTS = {
    "IF": "********.SH",  # 沪深300ETF期权
    "IH": "********.SH",  # 上证50ETF期权
    "IM": "********.SZ"   # 中证1000ETF期权
}

# 指数配置
INDEX_CODES = {
    "上证50": "000016.SH",
    "沪深300": "000300.SH",
    "中证1000": "000852.SH",
    "上证指数": "000001.SH",
    "深成指": "399001.SZ",
    "创业板指": "399006.SZ"
}

# ==================== 舆情监控配置 ====================

# 微信公众号列表
WECHAT_ACCOUNTS = [
    "财经早餐",
    "陆家嘴财经早餐", 
    "华泰期货研究",
    "中信期货研究",
    "国泰君安期货研究"
]

# 抖音账号列表
DOUYIN_ACCOUNTS = [
    "小张小张吃饭用缸",
    "清醒高师傅",
    "暖叔",
    "美股量化博士"
]

# 关键词监控
KEYWORDS = [
    "IF主力", "IH主力", "IM主力",
    "降息", "加息", "PMI",
    "英伟达", "AI", "人工智能",
    "期权", "波动率", "PCR",
    "北向资金", "南向资金",
    "两融", "杠杆"
]

# ==================== 浏览器配置 ====================

SELENIUM_CONFIG = {
    "browser": "chrome",
    "headless": False,  # 设为True可无头运行
    "window_size": (1920, 1080),
    "timeout": 30,
    "retry_times": 3
}

# ==================== 输出配置 ====================

# 输出目录配置
OUTPUT_DIRS = {
    "base": "output",
    "images": "output/images",
    "articles": "output/articles", 
    "reports": "output/reports",
    "temp": "output/temp"
}

# 文件命名规则
FILE_NAMING = {
    "date_format": "%Y%m%d",
    "time_format": "%H%M%S",
    "chart_prefix": "chart_",
    "article_prefix": "article_",
    "report_prefix": "report_"
}

# ==================== 图表配置 ====================

# 图表样式配置
CHART_CONFIG = {
    "theme": "plotly_white",
    "width": 1200,
    "height": 800,
    "font_family": "Arial, sans-serif",
    "font_size": 12,
    "title_font_size": 16,
    "save_formats": ["html", "png"]
}

# 图表列表配置
CHART_LIST = [
    {
        "name": "market_sentiment_panel",
        "title": "市场情绪面板",
        "type": "dashboard",
        "priority": 1
    },
    {
        "name": "margin_trading_scale",
        "title": "两融规模图", 
        "type": "line",
        "priority": 2
    },
    {
        "name": "volatility_indicators",
        "title": "波动率核心指标表",
        "type": "table",
        "priority": 3
    },
    {
        "name": "futures_price_trend",
        "title": "股指期货价格走势图",
        "type": "candlestick",
        "priority": 4
    },
    {
        "name": "iv_hv_trend",
        "title": "IV-HV走势图",
        "type": "line",
        "priority": 5
    },
    {
        "name": "pcr_trend",
        "title": "PCR走势图",
        "type": "line",
        "priority": 6
    },
    {
        "name": "volatility_smile",
        "title": "微笑曲线",
        "type": "line",
        "priority": 7
    },
    {
        "name": "volatility_term_structure",
        "title": "波动率期限结构",
        "type": "line",
        "priority": 8
    },
    {
        "name": "volatility_surface",
        "title": "波动率曲面",
        "type": "surface",
        "priority": 9
    },
    {
        "name": "iv_hv_spread",
        "title": "IV-HV价差图",
        "type": "line",
        "priority": 10
    },
    {
        "name": "option_oi_distribution",
        "title": "期权持仓分布",
        "type": "bar",
        "priority": 11
    },
    {
        "name": "capital_flow",
        "title": "北向/南向资金流",
        "type": "line",
        "priority": 12
    }
]

# ==================== 系统配置 ====================

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "1 day",
    "retention": "30 days",
    "log_file": "logs/system.log"
}

# 系统运行配置
SYSTEM_CONFIG = {
    "timezone": "Asia/Shanghai",
    "max_workers": 4,  # 并发处理数量
    "cache_enabled": True,
    "cache_ttl": 3600,  # 缓存时间（秒）
    "debug_mode": False
}

# ==================== 验证函数 ====================

def validate_config():
    """验证配置文件的完整性"""
    required_keys = [
        "OPENAI_CONFIG",
        "WIND_CONFIG", 
        "FUTURES_CONTRACTS",
        "OPTIONS_CONTRACTS",
        "INDEX_CODES"
    ]
    
    for key in required_keys:
        if key not in globals():
            raise ValueError(f"配置项 {key} 未找到")
    
    # 验证API密钥
    if OPENAI_CONFIG["api_key"] == "your_openai_api_key_here":
        raise ValueError("请设置有效的OpenAI API密钥")
    
    return True

# ==================== 环境变量支持 ====================

def load_from_env():
    """从环境变量加载配置（可选）"""
    if os.getenv("OPENAI_API_KEY"):
        OPENAI_CONFIG["api_key"] = os.getenv("OPENAI_API_KEY")
    
    if os.getenv("DEBUG_MODE"):
        SYSTEM_CONFIG["debug_mode"] = os.getenv("DEBUG_MODE").lower() == "true"

# 自动加载环境变量
load_from_env()

# 中国股指期货报告自动生成系统 - 配置文件
# 这是一个简化的配置文件，用于系统初始运行

import os
from datetime import datetime

# ==================== API配置 ====================

# OpenAI API配置
OPENAI_CONFIG = {
    "api_key": "your_openai_api_key_here",  # 请填入您的OpenAI API密钥
    "model": "gpt-4",
    "max_tokens": 4000,
    "temperature": 0.3,
    "timeout": 60
}

# Wind API配置
WIND_CONFIG = {
    "timeout": 30,
    "retry_times": 3,
    "retry_delay": 5
}

# ==================== 数据源配置 ====================

# 股指期货合约配置
FUTURES_CONTRACTS = {
    "IF": "IF.CFE",  # 沪深300股指期货
    "IH": "IH.CFE",  # 上证50股指期货
    "IM": "IM.CFE"   # 中证1000股指期货
}

# 期权合约配置
OPTIONS_CONTRACTS = {
    "IF": "10002273.SH",  # 沪深300ETF期权
    "IH": "10002510.SH",  # 上证50ETF期权
    "IM": "10003720.SZ"   # 中证1000ETF期权
}

# 指数配置
INDEX_CODES = {
    "上证50": "000016.SH",
    "沪深300": "000300.SH",
    "中证1000": "000852.SH",
    "上证指数": "000001.SH",
    "深成指": "399001.SZ",
    "创业板指": "399006.SZ"
}

# ==================== 输出配置 ====================

# 输出目录配置
OUTPUT_DIRS = {
    "base": "output",
    "images": "output/images",
    "articles": "output/articles", 
    "reports": "output/reports",
    "temp": "output/temp"
}

# ==================== 验证函数 ====================

def validate_config():
    """验证配置文件的完整性"""
    required_keys = [
        "OPENAI_CONFIG",
        "WIND_CONFIG", 
        "FUTURES_CONTRACTS",
        "OPTIONS_CONTRACTS",
        "INDEX_CODES"
    ]
    
    for key in required_keys:
        if key not in globals():
            raise ValueError(f"配置项 {key} 未找到")
    
    return True
